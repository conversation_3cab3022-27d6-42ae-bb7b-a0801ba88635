"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Download,
  Mail,
  MapPin,
  Phone,
  Linkedin,
  Award,
  Briefcase,
  GraduationCap,
  User,
  Target,
  Star,
  Globe,
  Code,
  Users,
} from "lucide-react"

export default function Resume() {
  const handlePrint = () => {
    window.print()
  }

  const skills = [
    { name: "Project Management", level: 90 },
    { name: "Strategic Planning", level: 85 },
    { name: "Leadership", level: 88 },
    { name: "Communication", level: 95 },
  ]

  const tools = ["Excel", "Slack", "MS Office", "Canva", "Google Workspace"]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-2">
      {/* Print Button */}
      <div className="max-w-6xl mx-auto mb-2 px-4 print:hidden">
        <Button
          onClick={handlePrint}
          className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
        >
          <Download className="w-4 h-4" />
          Download PDF
        </Button>
      </div>

      {/* Resume Container */}
      <div className="max-w-6xl mx-auto bg-white shadow-2xl print:shadow-none print:max-w-none rounded-lg overflow-hidden">
        {/* Page 1 */}
        <div className="page-break-after flex h-[10.5in] print:h-[10.5in]">
          {/* Left Sidebar */}
          <div className="w-1/3 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 text-white p-4">
            {/* Profile Section */}
            <div className="text-center mb-4">
              <h1 className="text-xl font-bold mb-1">IFEOMA WINIFRED</h1>
              <h2 className="text-lg font-light mb-3">ENUSHAI</h2>
              <div className="h-0.5 w-12 bg-gradient-to-r from-blue-400 to-indigo-500 mx-auto rounded-full"></div>
            </div>

            {/* Contact Information */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Mail className="w-3 h-3" />
                CONTACT
              </h3>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <MapPin className="w-2.5 h-2.5 text-blue-400 flex-shrink-0" />
                  <span>Lagos, Nigeria</span>
                </div>
                <div className="flex items-center gap-1">
                  <Phone className="w-2.5 h-2.5 text-blue-400 flex-shrink-0" />
                  <a href="tel:+2348023853088" className="hover:text-blue-400 transition-colors">
                    +2348023853088
                  </a>
                </div>
                <div className="flex items-center gap-1">
                  <Mail className="w-2.5 h-2.5 text-blue-400 flex-shrink-0" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-400 transition-colors break-all">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-1">
                  <Linkedin className="w-2.5 h-2.5 text-blue-400 flex-shrink-0" />
                  <a
                    href="https://linkedin.com/in/winifred-enushai"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:text-blue-400 transition-colors"
                  >
                    LinkedIn Profile
                  </a>
                </div>
              </div>
            </div>

            {/* Skills Progress */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Target className="w-3 h-3" />
                CORE SKILLS
              </h3>
              <div className="space-y-2">
                {skills.map((skill, index) => (
                  <div key={index}>
                    <div className="flex justify-between text-xs mb-0.5">
                      <span>{skill.name}</span>
                      <span>{skill.level}%</span>
                    </div>
                    <Progress value={skill.level} className="h-1 bg-slate-700" />
                  </div>
                ))}
              </div>
            </div>

            {/* Tools */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Code className="w-3 h-3" />
                TOOLS
              </h3>
              <div className="flex flex-wrap gap-1">
                {tools.map((tool, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="bg-slate-700 text-white hover:bg-slate-600 text-xs py-0 px-1"
                  >
                    {tool}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Languages */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Globe className="w-3 h-3" />
                LANGUAGES
              </h3>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between items-center">
                  <span>English</span>
                  <div className="flex gap-0.5">
                    {[1, 2, 3, 4, 5].map((i) => (
                      <Star key={i} className="w-2 h-2 fill-blue-400 text-blue-400" />
                    ))}
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span>French</span>
                  <div className="flex gap-0.5">
                    {[1, 2].map((i) => (
                      <Star key={i} className="w-2 h-2 fill-blue-400 text-blue-400" />
                    ))}
                    {[3, 4, 5].map((i) => (
                      <Star key={i} className="w-2 h-2 text-slate-600" />
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Education */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <GraduationCap className="w-3 h-3" />
                EDUCATION
              </h3>
              <div className="space-y-2">
                <div className="border-l-2 border-blue-400 pl-2">
                  <h4 className="font-semibold text-xs">BDS in-view</h4>
                  <p className="text-xs text-slate-300">UNILAG • 2018-2025</p>
                </div>
                <div className="border-l-2 border-indigo-400 pl-2">
                  <h4 className="font-semibold text-xs">WIPO Certificate</h4>
                  <p className="text-xs text-slate-300">Oct 2023</p>
                </div>
              </div>
            </div>

            {/* Certifications */}
            <div className="mb-4">
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Award className="w-3 h-3" />
                CERTIFICATIONS
              </h3>
              <div className="space-y-1 text-xs">
                <div className="bg-slate-800 p-1.5 rounded">
                  <p className="font-medium">CPM - PMI</p>
                  <p className="text-slate-400 text-xs">2023</p>
                </div>
                <div className="bg-slate-800 p-1.5 rounded">
                  <p className="font-medium">Tech Transfer Ambassador</p>
                  <p className="text-slate-400 text-xs">ITMO/IREX - July 2025</p>
                </div>
                <div className="bg-slate-800 p-1.5 rounded">
                  <p className="font-medium">Special Olympics Nigeria</p>
                </div>
                <div className="bg-slate-800 p-1.5 rounded">
                  <p className="font-medium">GTCO Autism Conference</p>
                </div>
              </div>
            </div>

            {/* Soft Skills */}
            <div>
              <h3 className="text-xs font-semibold mb-2 flex items-center gap-1">
                <Users className="w-3 h-3" />
                SOFT SKILLS
              </h3>
              <div className="grid grid-cols-2 gap-1 text-xs">
                {["Innovation", "Leadership", "Analysis", "Teamwork"].map((skill, index) => (
                  <div key={index} className="bg-slate-800 p-1 rounded text-center">
                    {skill}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Right Content */}
          <div className="w-2/3 p-4">
            {/* Professional Summary */}
            <div className="mb-4 border-l-4 border-l-blue-500 pl-3 bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-r-lg">
              <h2 className="text-lg font-bold text-slate-800 mb-2 flex items-center gap-2">
                <User className="w-4 h-4 text-blue-500" />
                Professional Summary
              </h2>
              <p className="text-xs text-slate-700 leading-relaxed">
                Highly motivated professional with passion for{" "}
                <span className="font-semibold text-blue-600">AI and Public Health</span>, experienced in strategic
                planning, content creation, and leadership. Adept at driving results through compelling communication
                and fostering collaborative environments. Eager to leverage diverse experiences in dynamic international
                settings like the{" "}
                <span className="font-semibold text-indigo-600">African Union Internship Program</span>.
              </p>
            </div>

            {/* Work Experience */}
            <div>
              <h2 className="text-lg font-bold text-slate-800 mb-3 flex items-center gap-2">
                <Briefcase className="w-4 h-4 text-blue-500" />
                Professional Experience
              </h2>

              <div className="space-y-3">
                {/* Sales Marketing Strategist */}
                <div className="border-l-4 border-l-indigo-400 pl-3 bg-gradient-to-r from-indigo-50 to-purple-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Sales Marketing Strategist</h3>
                      <p className="text-indigo-600 font-semibold text-xs">Luabi Travels • Lagos, Nigeria</p>
                    </div>
                    <Badge variant="outline" className="border-indigo-200 text-indigo-700 text-xs">
                      Mar-Apr 2024
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-indigo-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Developed comprehensive sales strategies to expand market reach and increase engagement
                    </li>
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-indigo-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Analyzed market trends to identify opportunities and optimize performance
                    </li>
                  </ul>
                </div>

                {/* Sales Representative Intern */}
                <div className="border-l-4 border-l-blue-400 pl-3 bg-gradient-to-r from-blue-50 to-cyan-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Sales Representative Intern</h3>
                      <p className="text-blue-600 font-semibold text-xs">Tech-Kest Limited • Lagos, Nigeria</p>
                    </div>
                    <Badge variant="outline" className="border-blue-200 text-blue-700 text-xs">
                      Jul-Aug 2023
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-blue-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Assisted in sales strategy development and customer acquisition
                    </li>
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-blue-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Conducted market research and supported client presentations
                    </li>
                  </ul>
                </div>

                {/* Event Manager */}
                <div className="border-l-4 border-l-green-400 pl-3 bg-gradient-to-r from-green-50 to-emerald-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Event Manager</h3>
                      <p className="text-green-600 font-semibold text-xs">Google Developer Student Club • Lagos</p>
                    </div>
                    <Badge variant="outline" className="border-green-200 text-green-700 text-xs">
                      Aug 2022-Jul 2023
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-green-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Orchestrated workshops, seminars, and hackathons for large student community
                    </li>
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-green-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Managed budgets, logistics, and led volunteer teams
                    </li>
                  </ul>
                </div>

                {/* Director */}
                <div className="border-l-4 border-l-purple-400 pl-3 bg-gradient-to-r from-purple-50 to-pink-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Director</h3>
                      <p className="text-purple-600 font-semibold text-xs">Childcare Initiative • Lagos, Nigeria</p>
                    </div>
                    <Badge variant="outline" className="border-purple-200 text-purple-700 text-xs">
                      Apr 2022-Present
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-purple-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Strategic direction for child welfare programs and fundraising efforts
                    </li>
                  </ul>
                </div>

                {/* Vice-President */}
                <div className="border-l-4 border-l-orange-400 pl-3 bg-gradient-to-r from-orange-50 to-red-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Vice-President</h3>
                      <p className="text-orange-600 font-semibold text-xs">AADMD, CMUL Chapter • New York, USA</p>
                    </div>
                    <Badge variant="outline" className="border-orange-200 text-orange-700 text-xs">
                      Mar 2024-Present
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-orange-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Led chapter initiatives in developmental medicine and dentistry
                    </li>
                  </ul>
                </div>

                {/* Teacher */}
                <div className="border-l-4 border-l-teal-400 pl-3 bg-gradient-to-r from-teal-50 to-cyan-50 p-2 rounded-r-lg">
                  <div className="flex justify-between items-start mb-1">
                    <div>
                      <h3 className="text-sm font-bold text-slate-800">Mathematics & Science Teacher</h3>
                      <p className="text-teal-600 font-semibold text-xs">Step-up Right Montessori • Lagos</p>
                    </div>
                    <Badge variant="outline" className="border-teal-200 text-teal-700 text-xs">
                      Nov-Dec 2021
                    </Badge>
                  </div>
                  <ul className="space-y-0.5 text-xs text-slate-700">
                    <li className="flex items-start gap-1">
                      <div className="w-1 h-1 bg-teal-400 rounded-full mt-1.5 flex-shrink-0"></div>
                      Designed engaging lessons and fostered critical thinking skills
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page 2 */}
        <div className="page-break-before h-[10.5in] print:h-[10.5in] p-4">
          {/* Key Research Project */}
          <div className="mb-4 border-l-4 border-l-rose-400 pl-3 bg-gradient-to-r from-rose-50 to-pink-50 p-3 rounded-r-lg">
            <h2 className="text-lg font-bold text-slate-800 mb-2 flex items-center gap-2">
              <Target className="w-4 h-4 text-rose-500" />
              Key Research Project
            </h2>
            <div>
              <h3 className="font-bold text-slate-800 mb-1 text-sm">
                Knowledge, Attitude, and Perception of Mothers on Dental Home Establishment
              </h3>
              <p className="text-slate-600 text-xs mb-2">Mushin LGA Research Study • Defended January 2024</p>
              <div className="flex flex-wrap gap-1 mb-2">
                <Badge className="bg-rose-100 text-rose-700 text-xs">Field Research</Badge>
                <Badge className="bg-pink-100 text-pink-700 text-xs">3 Healthcare Centers</Badge>
                <Badge className="bg-purple-100 text-purple-700 text-xs">Community Impact</Badge>
              </div>
              <p className="text-slate-700 text-xs">
                Conducted comprehensive field research emphasizing critical need for early oral health education in
                underserved communities. Interviewed mothers during vaccination days, revealing low awareness of dental
                home concept.
              </p>
            </div>
          </div>

          {/* Leadership & Achievements */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="border-l-4 border-l-emerald-400 pl-3 bg-gradient-to-r from-emerald-50 to-green-50 p-3 rounded-r-lg">
              <h3 className="text-sm font-bold text-slate-800 mb-2 flex items-center gap-1">
                <Users className="w-3 h-3 text-emerald-500" />
                Leadership Roles
              </h3>
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span>Financial Secretary</span>
                  <span className="text-slate-500">Dental Students</span>
                </div>
                <div className="flex justify-between">
                  <span>Course Representative</span>
                  <span className="text-slate-500">UNILAG</span>
                </div>
                <div className="flex justify-between">
                  <span>Campus Ambassador</span>
                  <span className="text-slate-500">Cowrywise</span>
                </div>
              </div>
            </div>

            <div className="border-l-4 border-l-amber-400 pl-3 bg-gradient-to-r from-amber-50 to-yellow-50 p-3 rounded-r-lg">
              <h3 className="text-sm font-bold text-slate-800 mb-2 flex items-center gap-1">
                <Star className="w-3 h-3 text-amber-500" />
                Key Achievements
              </h3>
              <div className="space-y-1 text-xs">
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-amber-400 rounded-full"></div>
                  <span>Led 15+ successful events</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-amber-400 rounded-full"></div>
                  <span>Managed 20+ volunteers</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-1.5 h-1.5 bg-amber-400 rounded-full"></div>
                  <span>Served 500+ community members</span>
                </div>
              </div>
            </div>
          </div>

          {/* Personal Development */}
          <div className="border-l-4 border-l-indigo-400 pl-3 bg-gradient-to-r from-indigo-50 to-blue-50 p-3 rounded-r-lg">
            <h2 className="text-sm font-bold text-slate-800 mb-2 flex items-center gap-2">
              <Target className="w-3 h-3 text-indigo-500" />
              Personal Development & Interests
            </h2>
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div>
                <h4 className="font-semibold text-slate-800 mb-1">15-Day Skipping Challenge</h4>
                <p className="text-slate-600">Enhanced fitness and determination (May 2024)</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-1">Kintsugi Art Therapy</h4>
                <p className="text-slate-600">Fostered resilience through art (June 2024)</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-1">Campaign Leadership</h4>
                <p className="text-slate-600">Multiple successful student leadership campaigns</p>
              </div>
              <div>
                <h4 className="font-semibold text-slate-800 mb-1">Community Engagement</h4>
                <p className="text-slate-600">Active volunteer in healthcare initiatives</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
