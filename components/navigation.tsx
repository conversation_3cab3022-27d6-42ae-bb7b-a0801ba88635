"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { FileText, Mail } from "lucide-react"

export default function Navigation() {
  const pathname = usePathname()

  return (
    <nav className="fixed top-4 left-4 z-50 print:hidden">
      <div className="flex gap-2 bg-white/90 backdrop-blur-sm rounded-lg shadow-lg p-2 border">
        <Link href="/">
          <Button
            variant={pathname === "/" ? "default" : "outline"}
            size="sm"
            className="flex items-center gap-2"
          >
            <FileText className="w-4 h-4" />
            Resume
          </Button>
        </Link>
        <Link href="/cover-letter">
          <Button
            variant={pathname === "/cover-letter" ? "default" : "outline"}
            size="sm"
            className="flex items-center gap-2"
          >
            <Mail className="w-4 h-4" />
            Cover Letter
          </Button>
        </Link>
      </div>
    </nav>
  )
}
